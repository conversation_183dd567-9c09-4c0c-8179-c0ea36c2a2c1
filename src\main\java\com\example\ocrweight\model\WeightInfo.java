package com.example.ocrweight.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Weight Information Model
 * 
 * Represents the extracted weight information from an image including
 * net weight (净重), tare weight (皮重), and gross weight (毛重).
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WeightInfo {
    
    /**
     * Image file name or path
     */
    private String imageName;
    
    /**
     * Net weight (净重) - the weight of the goods without packaging
     */
    private Double netWeight;
    
    /**
     * Net weight unit
     */
    private String netWeightUnit;
    
    /**
     * Tare weight (皮重) - the weight of the packaging/container
     */
    private Double tareWeight;
    
    /**
     * Tare weight unit
     */
    private String tareWeightUnit;
    
    /**
     * Gross weight (毛重) - the total weight including packaging
     */
    private Double grossWeight;
    
    /**
     * Gross weight unit
     */
    private String grossWeightUnit;
    
    /**
     * Raw OCR text extracted from the image
     */
    private String rawOcrText;
    
    /**
     * Processing timestamp
     */
    private LocalDateTime processedAt;
    
    /**
     * Processing status
     */
    private ProcessingStatus status;
    
    /**
     * Error message if processing failed
     */
    private String errorMessage;
    
    /**
     * Confidence score of the OCR result (0.0 to 1.0)
     */
    private Double confidence;
    
    /**
     * Processing Status Enum
     */
    public enum ProcessingStatus {
        SUCCESS,
        PARTIAL_SUCCESS,
        FAILED,
        NO_WEIGHT_FOUND
    }
    
    /**
     * Check if any weight information was found
     */
    public boolean hasWeightInfo() {
        return netWeight != null || tareWeight != null || grossWeight != null;
    }
    
    /**
     * Get a summary of found weights
     */
    public String getWeightSummary() {
        StringBuilder summary = new StringBuilder();
        if (netWeight != null) {
            summary.append("净重: ").append(netWeight);
            if (netWeightUnit != null) {
                summary.append(netWeightUnit);
            }
            summary.append(" ");
        }
        if (tareWeight != null) {
            summary.append("皮重: ").append(tareWeight);
            if (tareWeightUnit != null) {
                summary.append(tareWeightUnit);
            }
            summary.append(" ");
        }
        if (grossWeight != null) {
            summary.append("毛重: ").append(grossWeight);
            if (grossWeightUnit != null) {
                summary.append(grossWeightUnit);
            }
        }
        return summary.toString().trim();
    }
}
