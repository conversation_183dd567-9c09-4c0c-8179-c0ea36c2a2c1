# OCR Weight Extractor

一个基于Spring Boot 2的OCR重量信息提取应用，使用Tesseract OCR识别图片中的净重、皮重、毛重信息。

## 功能特性

- 🔍 **OCR文字识别**: 使用Tesseract OCR引擎识别图片中的中文和英文文字
- ⚖️ **重量信息提取**: 自动提取净重、皮重、毛重信息
- 📁 **批量处理**: 支持批量处理指定目录下的所有图片
- 🚀 **RESTful API**: 提供完整的REST API接口
- 🔧 **灵活配置**: 支持自定义OCR参数和重量提取规则
- 📊 **详细日志**: 完整的处理日志和错误信息

## 系统要求

- Java 8 或更高版本
- Maven 3.6+
- Tesseract OCR 引擎
- 中文语言包 (tessdata)

## 安装配置

### 1. 安装Tesseract OCR

#### Windows:
1. 下载Tesseract安装包: https://github.com/UB-Mannheim/tesseract/wiki
2. 安装到默认路径 (通常是 `C:\Program Files\Tesseract-OCR`)
3. 下载中文语言包 `chi_sim.traineddata` 到 `tessdata` 目录

#### Linux:
```bash
sudo apt-get install tesseract-ocr
sudo apt-get install tesseract-ocr-chi-sim
```

#### macOS:
```bash
brew install tesseract
brew install tesseract-lang
```

### 2. 配置tessdata路径

在 `application.yml` 中配置tessdata路径：
```yaml
ocr:
  tesseract:
    data-path: "C:/Program Files/Tesseract-OCR/tessdata"  # Windows
    # data-path: "/usr/share/tesseract-ocr/4.00/tessdata"  # Linux
```

### 3. 编译运行

```bash
# 克隆项目
git clone <repository-url>
cd ocr-weight-extractor

# 编译项目
mvn clean compile

# 运行应用
mvn spring-boot:run
```

应用将在 http://localhost:8080 启动

## API接口

### 1. 处理单个上传图片
```http
POST /api/ocr/process-image
Content-Type: multipart/form-data

file: <image-file>
```

### 2. 处理指定路径的图片文件
```http
POST /api/ocr/process-file
Content-Type: application/x-www-form-urlencoded

path=<image-file-path>
```

### 3. 批量处理目录下所有图片
```http
POST /api/ocr/process-directory
Content-Type: application/x-www-form-urlencoded

path=<directory-path>
```

### 4. 仅提取OCR文字（不进行重量提取）
```http
POST /api/ocr/extract-text
Content-Type: multipart/form-data

file: <image-file>
```

### 5. 健康检查
```http
GET /api/ocr/health
```

## 使用示例

### 处理当前目录下的图片

```bash
curl -X POST "http://localhost:8080/api/ocr/process-directory" \
     -d "path=."
```

### 上传图片处理

```bash
curl -X POST "http://localhost:8080/api/ocr/process-image" \
     -F "file=@your-image.jpg"
```

## 响应格式

```json
{
  "success": true,
  "message": "OCR processing completed successfully",
  "weightInfo": {
    "imageName": "example.jpg",
    "netWeight": 1000.0,
    "netWeightUnit": "kg",
    "tareWeight": 50.0,
    "tareWeightUnit": "kg",
    "grossWeight": 1050.0,
    "grossWeightUnit": "kg",
    "rawOcrText": "净重：1000kg 皮重：50kg 毛重：1050kg",
    "processedAt": "2024-01-01T12:00:00",
    "status": "SUCCESS"
  },
  "totalImages": 1,
  "successfulImages": 1,
  "failedImages": 0
}
```

## 配置说明

### OCR配置
```yaml
ocr:
  tesseract:
    data-path: "tessdata"           # tessdata目录路径
    language: "chi_sim+eng"         # 识别语言
    oem: 3                          # OCR引擎模式
    psm: 6                          # 页面分割模式
```

### 重量提取规则
```yaml
weight:
  patterns:
    net-weight: 
      - "净重[：:]*\\s*(\\d+(?:\\.\\d+)?)\\s*([kKgG克吨tT]+)"
    tare-weight:
      - "皮重[：:]*\\s*(\\d+(?:\\.\\d+)?)\\s*([kKgG克吨tT]+)"
    gross-weight:
      - "毛重[：:]*\\s*(\\d+(?:\\.\\d+)?)\\s*([kKgG克吨tT]+)"
```

## 支持的图片格式

- JPG/JPEG
- PNG
- BMP
- TIFF
- GIF

## 故障排除

### 1. Tesseract找不到
确保Tesseract已正确安装并配置了正确的路径。

### 2. 中文识别效果差
确保已安装中文语言包 `chi_sim.traineddata`。

### 3. 内存不足
可以调整JVM内存参数：
```bash
java -Xmx2g -jar target/ocr-weight-extractor-1.0.0.jar
```

## 开发说明

### 项目结构
```
src/main/java/com/example/ocrweight/
├── controller/          # REST API控制器
├── service/            # 业务逻辑服务
├── model/              # 数据模型
├── config/             # 配置类
└── OcrWeightExtractorApplication.java
```

### 扩展重量提取规则
可以在 `WeightConfig` 中添加新的正则表达式模式来支持更多的重量格式。

## 许可证

MIT License
