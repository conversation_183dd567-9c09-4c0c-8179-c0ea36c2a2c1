package com.example.ocrweight.service;

import com.example.ocrweight.config.OcrConfig;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.tess4j.ITesseract;
import net.sourceforge.tess4j.Tesseract;
import net.sourceforge.tess4j.TesseractException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

/**
 * OCR Service
 * 
 * Service for performing Optical Character Recognition on images using Tesseract.
 */
@Slf4j
@Service
public class OcrService {
    
    @Autowired
    private OcrConfig ocrConfig;
    
    private ITesseract tesseract;
    
    @PostConstruct
    public void init() {
        tesseract = new Tesseract();
        
        // Configure Tesseract
        if (ocrConfig.getTesseract().getDataPath() != null) {
            tesseract.setDatapath(ocrConfig.getTesseract().getDataPath());
        }
        
        tesseract.setLanguage(ocrConfig.getTesseract().getLanguage());
        tesseract.setOcrEngineMode(ocrConfig.getTesseract().getOem());
        tesseract.setPageSegMode(ocrConfig.getTesseract().getPsm());
        
        log.info("OCR Service initialized with language: {}, datapath: {}", 
                ocrConfig.getTesseract().getLanguage(), 
                ocrConfig.getTesseract().getDataPath());
    }
    
    /**
     * Extract text from image file
     * 
     * @param imageFile the image file to process
     * @return extracted text
     * @throws TesseractException if OCR processing fails
     * @throws IOException if image reading fails
     */
    public String extractText(File imageFile) throws TesseractException, IOException {
        log.debug("Starting OCR processing for file: {}", imageFile.getName());
        
        BufferedImage image = ImageIO.read(imageFile);
        if (image == null) {
            throw new IOException("Unable to read image file: " + imageFile.getName());
        }
        
        // Apply preprocessing if enabled
        if (ocrConfig.getImage().getPreprocessing().isEnhance()) {
            image = preprocessImage(image);
        }
        
        String result = tesseract.doOCR(image);
        log.debug("OCR completed for file: {}, extracted {} characters", 
                imageFile.getName(), result.length());
        
        return result;
    }
    
    /**
     * Extract text from BufferedImage
     * 
     * @param image the image to process
     * @return extracted text
     * @throws TesseractException if OCR processing fails
     */
    public String extractText(BufferedImage image) throws TesseractException {
        log.debug("Starting OCR processing for BufferedImage");
        
        // Apply preprocessing if enabled
        if (ocrConfig.getImage().getPreprocessing().isEnhance()) {
            image = preprocessImage(image);
        }
        
        String result = tesseract.doOCR(image);
        log.debug("OCR completed for BufferedImage, extracted {} characters", result.length());
        
        return result;
    }
    
    /**
     * Preprocess image to improve OCR accuracy
     * 
     * @param originalImage the original image
     * @return preprocessed image
     */
    private BufferedImage preprocessImage(BufferedImage originalImage) {
        log.debug("Preprocessing image for better OCR accuracy");
        
        double scaleFactor = ocrConfig.getImage().getPreprocessing().getScaleFactor();
        
        // Calculate new dimensions
        int newWidth = (int) (originalImage.getWidth() * scaleFactor);
        int newHeight = (int) (originalImage.getHeight() * scaleFactor);
        
        // Create scaled image
        BufferedImage scaledImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = scaledImage.createGraphics();
        
        // Set rendering hints for better quality
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // Draw scaled image
        g2d.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
        g2d.dispose();
        
        // Convert to grayscale for better OCR
        BufferedImage grayscaleImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_BYTE_GRAY);
        Graphics2D g2dGray = grayscaleImage.createGraphics();
        g2dGray.drawImage(scaledImage, 0, 0, null);
        g2dGray.dispose();
        
        log.debug("Image preprocessed: original size {}x{}, new size {}x{}", 
                originalImage.getWidth(), originalImage.getHeight(), newWidth, newHeight);
        
        return grayscaleImage;
    }
    
    /**
     * Check if the image format is supported
     * 
     * @param fileName the image file name
     * @return true if supported, false otherwise
     */
    public boolean isSupportedFormat(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return false;
        }
        
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        return ocrConfig.getImage().getSupportedFormats().contains(extension);
    }
}
