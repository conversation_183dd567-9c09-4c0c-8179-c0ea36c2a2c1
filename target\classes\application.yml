server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: ocr-weight-extractor
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB

# OCR Configuration
ocr:
  tesseract:
    # Tesseract data path - adjust according to your tessdata installation
    data-path: "tessdata"
    # Language for OCR (chi_sim for Simplified Chinese, eng for English)
    language: "chi_sim+eng"
    # OCR Engine Mode (3 = Default, based on what is available)
    oem: 3
    # Page Segmentation Mode (6 = Uniform block of text)
    psm: 6
  
  # Image processing settings
  image:
    # Supported image formats
    supported-formats: ["jpg", "jpeg", "png", "bmp", "tiff", "gif"]
    # Image preprocessing options
    preprocessing:
      # Enable image enhancement before OCR
      enhance: true
      # Resize factor for better OCR accuracy
      scale-factor: 2.0

# Weight extraction patterns (regex patterns for Chinese weight terms)
weight:
  patterns:
    net-weight: ["净重[：:]*\\s*(\\d+(?:\\.\\d+)?)\\s*([kKgG克吨tT]+)", "净重[：:]*\\s*(\\d+(?:\\.\\d+)?)"]
    tare-weight: ["皮重[：:]*\\s*(\\d+(?:\\.\\d+)?)\\s*([kKgG克吨tT]+)", "皮重[：:]*\\s*(\\d+(?:\\.\\d+)?)"]
    gross-weight: ["毛重[：:]*\\s*(\\d+(?:\\.\\d+)?)\\s*([kKgG克吨tT]+)", "毛重[：:]*\\s*(\\d+(?:\\.\\d+)?)"]

# Logging configuration
logging:
  level:
    com.example.ocrweight: DEBUG
    net.sourceforge.tess4j: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/ocr-weight-extractor.log
