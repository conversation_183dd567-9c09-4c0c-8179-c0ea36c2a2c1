package com.example.ocrweight.service;

import com.example.ocrweight.model.WeightInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * Batch Processing Service
 * 
 * Service for processing multiple images in a directory.
 */
@Slf4j
@Service
public class BatchProcessingService {
    
    @Autowired
    private OcrService ocrService;
    
    @Autowired
    private WeightExtractionService weightExtractionService;
    
    private final ExecutorService executorService = Executors.newFixedThreadPool(4);
    
    /**
     * Process all images in a directory
     * 
     * @param directoryPath the path to the directory containing images
     * @return list of WeightInfo objects for all processed images
     */
    public List<WeightInfo> processDirectory(String directoryPath) {
        log.info("Starting batch processing for directory: {}", directoryPath);
        
        File directory = new File(directoryPath);
        if (!directory.exists() || !directory.isDirectory()) {
            log.error("Directory does not exist or is not a directory: {}", directoryPath);
            return List.of(createErrorWeightInfo("Invalid directory: " + directoryPath, directoryPath));
        }
        
        // Get all image files in the directory
        File[] imageFiles = directory.listFiles(this::isImageFile);
        if (imageFiles == null || imageFiles.length == 0) {
            log.warn("No image files found in directory: {}", directoryPath);
            return List.of();
        }
        
        log.info("Found {} image files in directory: {}", imageFiles.length, directoryPath);
        
        // Process images in parallel
        List<CompletableFuture<WeightInfo>> futures = Arrays.stream(imageFiles)
                .map(this::processImageAsync)
                .collect(Collectors.toList());
        
        // Wait for all processing to complete and collect results
        List<WeightInfo> results = new ArrayList<>();
        for (CompletableFuture<WeightInfo> future : futures) {
            try {
                results.add(future.get());
            } catch (Exception e) {
                log.error("Error processing image", e);
                results.add(createErrorWeightInfo("Processing failed: " + e.getMessage(), "unknown"));
            }
        }
        
        log.info("Batch processing completed. Processed {} images", results.size());
        return results;
    }
    
    /**
     * Process a single image asynchronously
     * 
     * @param imageFile the image file to process
     * @return CompletableFuture containing the WeightInfo result
     */
    private CompletableFuture<WeightInfo> processImageAsync(File imageFile) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.debug("Processing image: {}", imageFile.getName());
                
                // Extract text using OCR
                String ocrText = ocrService.extractText(imageFile);
                
                // Extract weight information
                WeightInfo weightInfo = weightExtractionService.extractWeightInfo(ocrText, imageFile.getName());
                
                log.debug("Completed processing image: {}", imageFile.getName());
                return weightInfo;
                
            } catch (Exception e) {
                log.error("Error processing image: {}", imageFile.getName(), e);
                return createErrorWeightInfo("Processing failed: " + e.getMessage(), imageFile.getName());
            }
        }, executorService);
    }
    
    /**
     * Process a list of specific image files
     * 
     * @param imageFiles list of image files to process
     * @return list of WeightInfo objects for all processed images
     */
    public List<WeightInfo> processImageFiles(List<File> imageFiles) {
        log.info("Starting batch processing for {} image files", imageFiles.size());
        
        // Filter out non-image files
        List<File> validImageFiles = imageFiles.stream()
                .filter(this::isImageFile)
                .collect(Collectors.toList());
        
        if (validImageFiles.isEmpty()) {
            log.warn("No valid image files found in the provided list");
            return List.of();
        }
        
        log.info("Processing {} valid image files", validImageFiles.size());
        
        // Process images in parallel
        List<CompletableFuture<WeightInfo>> futures = validImageFiles.stream()
                .map(this::processImageAsync)
                .collect(Collectors.toList());
        
        // Wait for all processing to complete and collect results
        List<WeightInfo> results = new ArrayList<>();
        for (CompletableFuture<WeightInfo> future : futures) {
            try {
                results.add(future.get());
            } catch (Exception e) {
                log.error("Error processing image", e);
                results.add(createErrorWeightInfo("Processing failed: " + e.getMessage(), "unknown"));
            }
        }
        
        log.info("Batch processing completed. Processed {} images", results.size());
        return results;
    }
    
    /**
     * Check if a file is an image file based on its extension
     * 
     * @param file the file to check
     * @return true if the file is an image, false otherwise
     */
    private boolean isImageFile(File file) {
        if (file == null || !file.isFile()) {
            return false;
        }
        
        return ocrService.isSupportedFormat(file.getName());
    }
    
    /**
     * Create a WeightInfo object for error cases
     * 
     * @param errorMessage the error message
     * @param imageName the image name
     * @return WeightInfo object with error status
     */
    private WeightInfo createErrorWeightInfo(String errorMessage, String imageName) {
        return WeightInfo.builder()
                .imageName(imageName)
                .status(WeightInfo.ProcessingStatus.FAILED)
                .errorMessage(errorMessage)
                .processedAt(LocalDateTime.now())
                .build();
    }
    
    /**
     * Shutdown the executor service
     */
    public void shutdown() {
        executorService.shutdown();
    }
}
