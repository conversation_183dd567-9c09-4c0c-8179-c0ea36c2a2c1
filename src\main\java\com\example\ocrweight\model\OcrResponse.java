package com.example.ocrweight.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * OCR Response Model
 * 
 * Represents the response from OCR processing operations.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OcrResponse {
    
    /**
     * Processing success status
     */
    private boolean success;
    
    /**
     * Response message
     */
    private String message;
    
    /**
     * Single weight information (for single image processing)
     */
    private WeightInfo weightInfo;
    
    /**
     * Multiple weight information (for batch processing)
     */
    private List<WeightInfo> weightInfoList;
    
    /**
     * Total number of images processed
     */
    private Integer totalImages;
    
    /**
     * Number of successfully processed images
     */
    private Integer successfulImages;
    
    /**
     * Number of failed images
     */
    private Integer failedImages;
    
    /**
     * Processing start time
     */
    private LocalDateTime startTime;
    
    /**
     * Processing end time
     */
    private LocalDateTime endTime;
    
    /**
     * Processing duration in milliseconds
     */
    private Long processingTimeMs;
    
    /**
     * Error details if processing failed
     */
    private String errorDetails;
    
    /**
     * Create a successful single image response
     */
    public static OcrResponse success(WeightInfo weightInfo) {
        return OcrResponse.builder()
                .success(true)
                .message("OCR processing completed successfully")
                .weightInfo(weightInfo)
                .totalImages(1)
                .successfulImages(weightInfo.getStatus() == WeightInfo.ProcessingStatus.SUCCESS || 
                                weightInfo.getStatus() == WeightInfo.ProcessingStatus.PARTIAL_SUCCESS ? 1 : 0)
                .failedImages(weightInfo.getStatus() == WeightInfo.ProcessingStatus.FAILED ? 1 : 0)
                .endTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * Create a successful batch processing response
     */
    public static OcrResponse success(List<WeightInfo> weightInfoList, LocalDateTime startTime) {
        long successful = weightInfoList.stream()
                .mapToLong(w -> w.getStatus() == WeightInfo.ProcessingStatus.SUCCESS || 
                              w.getStatus() == WeightInfo.ProcessingStatus.PARTIAL_SUCCESS ? 1 : 0)
                .sum();
        
        LocalDateTime endTime = LocalDateTime.now();
        
        return OcrResponse.builder()
                .success(true)
                .message("Batch OCR processing completed")
                .weightInfoList(weightInfoList)
                .totalImages(weightInfoList.size())
                .successfulImages((int) successful)
                .failedImages(weightInfoList.size() - (int) successful)
                .startTime(startTime)
                .endTime(endTime)
                .build();
    }
    
    /**
     * Create an error response
     */
    public static OcrResponse error(String message, String errorDetails) {
        return OcrResponse.builder()
                .success(false)
                .message(message)
                .errorDetails(errorDetails)
                .endTime(LocalDateTime.now())
                .build();
    }
}
