package com.example.ocrweight.service;

import com.example.ocrweight.config.WeightConfig;
import com.example.ocrweight.model.WeightInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Weight Extraction Service
 * 
 * Service for extracting weight information (净重、皮重、毛重) from OCR text.
 */
@Slf4j
@Service
public class WeightExtractionService {
    
    @Autowired
    private WeightConfig weightConfig;
    
    /**
     * Extract weight information from OCR text
     * 
     * @param ocrText the OCR extracted text
     * @param imageName the name of the processed image
     * @return WeightInfo object containing extracted weight information
     */
    public WeightInfo extractWeightInfo(String ocrText, String imageName) {
        log.debug("Extracting weight information from OCR text for image: {}", imageName);
        
        WeightInfo.WeightInfoBuilder builder = WeightInfo.builder()
                .imageName(imageName)
                .rawOcrText(ocrText)
                .processedAt(LocalDateTime.now());
        
        try {
            // Extract net weight (净重)
            extractNetWeight(ocrText, builder);
            
            // Extract tare weight (皮重)
            extractTareWeight(ocrText, builder);
            
            // Extract gross weight (毛重)
            extractGrossWeight(ocrText, builder);
            
            WeightInfo weightInfo = builder.build();
            
            // Determine processing status
            if (weightInfo.hasWeightInfo()) {
                if (weightInfo.getNetWeight() != null && 
                    weightInfo.getTareWeight() != null && 
                    weightInfo.getGrossWeight() != null) {
                    weightInfo.setStatus(WeightInfo.ProcessingStatus.SUCCESS);
                } else {
                    weightInfo.setStatus(WeightInfo.ProcessingStatus.PARTIAL_SUCCESS);
                }
                log.info("Weight extraction successful for {}: {}", imageName, weightInfo.getWeightSummary());
            } else {
                weightInfo.setStatus(WeightInfo.ProcessingStatus.NO_WEIGHT_FOUND);
                log.warn("No weight information found in image: {}", imageName);
            }
            
            return weightInfo;
            
        } catch (Exception e) {
            log.error("Error extracting weight information from image: {}", imageName, e);
            return builder
                    .status(WeightInfo.ProcessingStatus.FAILED)
                    .errorMessage("Weight extraction failed: " + e.getMessage())
                    .build();
        }
    }
    
    /**
     * Extract net weight (净重) from OCR text
     */
    private void extractNetWeight(String ocrText, WeightInfo.WeightInfoBuilder builder) {
        List<String> patterns = weightConfig.getPatterns().get("net-weight");
        if (patterns != null) {
            for (String patternStr : patterns) {
                Pattern pattern = Pattern.compile(patternStr, Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(ocrText);
                
                if (matcher.find()) {
                    try {
                        double weight = Double.parseDouble(matcher.group(1));
                        builder.netWeight(weight);
                        
                        // Extract unit if available
                        if (matcher.groupCount() > 1 && matcher.group(2) != null) {
                            builder.netWeightUnit(normalizeUnit(matcher.group(2)));
                        }
                        
                        log.debug("Net weight extracted: {} {}", weight, 
                                matcher.groupCount() > 1 ? matcher.group(2) : "");
                        break;
                    } catch (NumberFormatException e) {
                        log.warn("Failed to parse net weight value: {}", matcher.group(1));
                    }
                }
            }
        }
    }
    
    /**
     * Extract tare weight (皮重) from OCR text
     */
    private void extractTareWeight(String ocrText, WeightInfo.WeightInfoBuilder builder) {
        List<String> patterns = weightConfig.getPatterns().get("tare-weight");
        if (patterns != null) {
            for (String patternStr : patterns) {
                Pattern pattern = Pattern.compile(patternStr, Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(ocrText);
                
                if (matcher.find()) {
                    try {
                        double weight = Double.parseDouble(matcher.group(1));
                        builder.tareWeight(weight);
                        
                        // Extract unit if available
                        if (matcher.groupCount() > 1 && matcher.group(2) != null) {
                            builder.tareWeightUnit(normalizeUnit(matcher.group(2)));
                        }
                        
                        log.debug("Tare weight extracted: {} {}", weight, 
                                matcher.groupCount() > 1 ? matcher.group(2) : "");
                        break;
                    } catch (NumberFormatException e) {
                        log.warn("Failed to parse tare weight value: {}", matcher.group(1));
                    }
                }
            }
        }
    }
    
    /**
     * Extract gross weight (毛重) from OCR text
     */
    private void extractGrossWeight(String ocrText, WeightInfo.WeightInfoBuilder builder) {
        List<String> patterns = weightConfig.getPatterns().get("gross-weight");
        if (patterns != null) {
            for (String patternStr : patterns) {
                Pattern pattern = Pattern.compile(patternStr, Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(ocrText);
                
                if (matcher.find()) {
                    try {
                        double weight = Double.parseDouble(matcher.group(1));
                        builder.grossWeight(weight);
                        
                        // Extract unit if available
                        if (matcher.groupCount() > 1 && matcher.group(2) != null) {
                            builder.grossWeightUnit(normalizeUnit(matcher.group(2)));
                        }
                        
                        log.debug("Gross weight extracted: {} {}", weight, 
                                matcher.groupCount() > 1 ? matcher.group(2) : "");
                        break;
                    } catch (NumberFormatException e) {
                        log.warn("Failed to parse gross weight value: {}", matcher.group(1));
                    }
                }
            }
        }
    }
    
    /**
     * Normalize weight units to standard format
     */
    private String normalizeUnit(String unit) {
        if (unit == null) return null;
        
        String normalized = unit.toLowerCase().trim();
        
        // Convert various unit representations to standard forms
        switch (normalized) {
            case "kg":
            case "k":
            case "千克":
            case "公斤":
                return "kg";
            case "g":
            case "克":
                return "g";
            case "t":
            case "吨":
                return "t";
            default:
                return unit; // Return original if no mapping found
        }
    }
}
