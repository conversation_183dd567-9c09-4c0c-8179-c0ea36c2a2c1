package com.example.ocrweight.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * OCR Configuration Properties
 * 
 * Configuration class for OCR and weight extraction settings.
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ocr")
public class OcrConfig {
    
    /**
     * Tesseract OCR configuration
     */
    private Tesseract tesseract = new Tesseract();
    
    /**
     * Image processing configuration
     */
    private Image image = new Image();
    
    @Data
    public static class Tesseract {
        /**
         * Path to tessdata directory
         */
        private String dataPath = "tessdata";
        
        /**
         * OCR language (e.g., "chi_sim+eng" for Chinese and English)
         */
        private String language = "chi_sim+eng";
        
        /**
         * OCR Engine Mode
         */
        private int oem = 3;
        
        /**
         * Page Segmentation Mode
         */
        private int psm = 6;
    }
    
    @Data
    public static class Image {
        /**
         * Supported image formats
         */
        private List<String> supportedFormats = Arrays.asList("jpg", "jpeg", "png", "bmp", "tiff", "gif");
        
        /**
         * Image preprocessing settings
         */
        private Preprocessing preprocessing = new Preprocessing();
        
        @Data
        public static class Preprocessing {
            /**
             * Enable image enhancement
             */
            private boolean enhance = true;
            
            /**
             * Scale factor for image resizing
             */
            private double scaleFactor = 2.0;
        }
    }
}

/**
 * Weight extraction patterns configuration
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "weight")
class WeightConfig {
    
    /**
     * Regex patterns for extracting different types of weights
     */
    private Map<String, List<String>> patterns = createDefaultPatterns();

    private Map<String, List<String>> createDefaultPatterns() {
        Map<String, List<String>> patterns = new HashMap<>();
        patterns.put("net-weight", Arrays.asList(
            "净重[：:]*\\s*(\\d+(?:\\.\\d+)?)\\s*([kKgG克吨tT]+)",
            "净重[：:]*\\s*(\\d+(?:\\.\\d+)?)"
        ));
        patterns.put("tare-weight", Arrays.asList(
            "皮重[：:]*\\s*(\\d+(?:\\.\\d+)?)\\s*([kKgG克吨tT]+)",
            "皮重[：:]*\\s*(\\d+(?:\\.\\d+)?)"
        ));
        patterns.put("gross-weight", Arrays.asList(
            "毛重[：:]*\\s*(\\d+(?:\\.\\d+)?)\\s*([kKgG克吨tT]+)",
            "毛重[：:]*\\s*(\\d+(?:\\.\\d+)?)"
        ));
        return patterns;
    }
}
