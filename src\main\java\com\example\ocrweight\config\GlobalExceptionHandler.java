package com.example.ocrweight.config;

import com.example.ocrweight.model.OcrResponse;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.tess4j.TesseractException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import java.io.IOException;

/**
 * Global Exception Handler
 * 
 * Handles exceptions globally across the application.
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    /**
     * Handle Tesseract OCR exceptions
     */
    @ExceptionHandler(TesseractException.class)
    public ResponseEntity<OcrResponse> handleTesseractException(TesseractException e) {
        log.error("Tesseract OCR error", e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(OcrResponse.error("OCR processing failed", e.getMessage()));
    }
    
    /**
     * Handle IO exceptions
     */
    @ExceptionHandler(IOException.class)
    public ResponseEntity<OcrResponse> handleIOException(IOException e) {
        log.error("IO error", e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(OcrResponse.error("File processing error", e.getMessage()));
    }
    
    /**
     * Handle file upload size exceeded
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<OcrResponse> handleMaxUploadSizeExceeded(MaxUploadSizeExceededException e) {
        log.error("File upload size exceeded", e);
        return ResponseEntity.status(HttpStatus.PAYLOAD_TOO_LARGE)
                .body(OcrResponse.error("File too large", "Maximum file size exceeded"));
    }
    
    /**
     * Handle illegal argument exceptions
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<OcrResponse> handleIllegalArgumentException(IllegalArgumentException e) {
        log.error("Invalid argument", e);
        return ResponseEntity.badRequest()
                .body(OcrResponse.error("Invalid argument", e.getMessage()));
    }
    
    /**
     * Handle all other exceptions
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<OcrResponse> handleGenericException(Exception e) {
        log.error("Unexpected error", e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(OcrResponse.error("Internal server error", "An unexpected error occurred"));
    }
}
