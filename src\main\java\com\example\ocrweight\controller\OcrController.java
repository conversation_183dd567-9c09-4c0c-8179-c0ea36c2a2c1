package com.example.ocrweight.controller;

import com.example.ocrweight.model.OcrResponse;
import com.example.ocrweight.model.WeightInfo;
import com.example.ocrweight.service.BatchProcessingService;
import com.example.ocrweight.service.OcrService;
import com.example.ocrweight.service.WeightExtractionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.time.LocalDateTime;
import java.util.List;

/**
 * OCR Controller
 * 
 * REST API controller for OCR and weight extraction operations.
 */
@Slf4j
@RestController
@RequestMapping("/ocr")
@CrossOrigin(origins = "*")
public class OcrController {
    
    @Autowired
    private OcrService ocrService;
    
    @Autowired
    private WeightExtractionService weightExtractionService;
    
    @Autowired
    private BatchProcessingService batchProcessingService;
    
    /**
     * Process a single uploaded image file
     * 
     * @param file the uploaded image file
     * @return OCR response with extracted weight information
     */
    @PostMapping(value = "/process-image", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<OcrResponse> processImage(@RequestParam("file") MultipartFile file) {
        log.info("Processing uploaded image: {}", file.getOriginalFilename());
        
        try {
            // Validate file
            if (file.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(OcrResponse.error("File is empty", "No file content provided"));
            }
            
            if (!ocrService.isSupportedFormat(file.getOriginalFilename())) {
                return ResponseEntity.badRequest()
                        .body(OcrResponse.error("Unsupported file format", 
                                "Supported formats: jpg, jpeg, png, bmp, tiff, gif"));
            }
            
            // Convert MultipartFile to BufferedImage
            BufferedImage image = ImageIO.read(file.getInputStream());
            if (image == null) {
                return ResponseEntity.badRequest()
                        .body(OcrResponse.error("Invalid image file", "Unable to read image content"));
            }
            
            // Extract text using OCR
            String ocrText = ocrService.extractText(image);
            
            // Extract weight information
            WeightInfo weightInfo = weightExtractionService.extractWeightInfo(ocrText, file.getOriginalFilename());
            
            return ResponseEntity.ok(OcrResponse.success(weightInfo));
            
        } catch (Exception e) {
            log.error("Error processing uploaded image: {}", file.getOriginalFilename(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(OcrResponse.error("Processing failed", e.getMessage()));
        }
    }
    
    /**
     * Process all images in a specified directory
     * 
     * @param directoryPath the path to the directory containing images
     * @return OCR response with extracted weight information for all images
     */
    @PostMapping("/process-directory")
    public ResponseEntity<OcrResponse> processDirectory(@RequestParam("path") String directoryPath) {
        log.info("Processing directory: {}", directoryPath);
        
        try {
            LocalDateTime startTime = LocalDateTime.now();
            
            // Process all images in the directory
            List<WeightInfo> weightInfoList = batchProcessingService.processDirectory(directoryPath);
            
            if (weightInfoList.isEmpty()) {
                return ResponseEntity.ok(OcrResponse.error("No images found", 
                        "No valid image files found in the specified directory"));
            }
            
            return ResponseEntity.ok(OcrResponse.success(weightInfoList, startTime));
            
        } catch (Exception e) {
            log.error("Error processing directory: {}", directoryPath, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(OcrResponse.error("Directory processing failed", e.getMessage()));
        }
    }
    
    /**
     * Process a specific image file by file path
     * 
     * @param filePath the path to the image file
     * @return OCR response with extracted weight information
     */
    @PostMapping("/process-file")
    public ResponseEntity<OcrResponse> processFile(@RequestParam("path") String filePath) {
        log.info("Processing file: {}", filePath);
        
        try {
            File imageFile = new File(filePath);
            
            // Validate file
            if (!imageFile.exists()) {
                return ResponseEntity.badRequest()
                        .body(OcrResponse.error("File not found", "The specified file does not exist"));
            }
            
            if (!imageFile.isFile()) {
                return ResponseEntity.badRequest()
                        .body(OcrResponse.error("Invalid file", "The specified path is not a file"));
            }
            
            if (!ocrService.isSupportedFormat(imageFile.getName())) {
                return ResponseEntity.badRequest()
                        .body(OcrResponse.error("Unsupported file format", 
                                "Supported formats: jpg, jpeg, png, bmp, tiff, gif"));
            }
            
            // Extract text using OCR
            String ocrText = ocrService.extractText(imageFile);
            
            // Extract weight information
            WeightInfo weightInfo = weightExtractionService.extractWeightInfo(ocrText, imageFile.getName());
            
            return ResponseEntity.ok(OcrResponse.success(weightInfo));
            
        } catch (Exception e) {
            log.error("Error processing file: {}", filePath, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(OcrResponse.error("File processing failed", e.getMessage()));
        }
    }
    
    /**
     * Get OCR text only (without weight extraction) from uploaded image
     * 
     * @param file the uploaded image file
     * @return raw OCR text
     */
    @PostMapping(value = "/extract-text", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<String> extractText(@RequestParam("file") MultipartFile file) {
        log.info("Extracting text from uploaded image: {}", file.getOriginalFilename());
        
        try {
            // Validate file
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().body("File is empty");
            }
            
            if (!ocrService.isSupportedFormat(file.getOriginalFilename())) {
                return ResponseEntity.badRequest().body("Unsupported file format");
            }
            
            // Convert MultipartFile to BufferedImage
            BufferedImage image = ImageIO.read(file.getInputStream());
            if (image == null) {
                return ResponseEntity.badRequest().body("Invalid image file");
            }
            
            // Extract text using OCR
            String ocrText = ocrService.extractText(image);
            
            return ResponseEntity.ok(ocrText);
            
        } catch (Exception e) {
            log.error("Error extracting text from uploaded image: {}", file.getOriginalFilename(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Text extraction failed: " + e.getMessage());
        }
    }
    
    /**
     * Health check endpoint
     * 
     * @return simple health status
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("OCR Weight Extractor is running");
    }
}
